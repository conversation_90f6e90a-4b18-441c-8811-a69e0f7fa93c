
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.utils import timezone
from datetime import datetime, timedelta
import csv
import json

from .models import Association, Member, Payment, Loan, LoanPayment
from . import firebase

# Login view
def login_view(request):
    if request.user.is_authenticated:
        return redirect('dashboard')

    if request.method == 'POST':
        username = request.POST['username']
        password = request.POST['password']
        user = authenticate(request, username=username, password=password)
        if user:
            if user.is_active:
                # إعطاء صلاحيات أساسية للمستخدم إذا لم تكن موجودة
                ensure_user_has_basic_permissions(user)

                login(request, user)
                next_url = request.GET.get('next', 'dashboard')
                messages.success(request, f'مرحباً بك {user.get_full_name() or user.username}!')
                return redirect(next_url)
            else:
                messages.error(request, 'حسابك معطل. يرجى التواصل مع المدير.')
        else:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة.')
    return render(request, 'login.html')

def ensure_user_has_basic_permissions(user):
    """التأكد من أن المستخدم لديه صلاحيات أساسية"""
    from django.contrib.auth.models import Group, Permission

    # إذا كان المستخدم مدير أو موظف، لا نحتاج لفعل شيء
    if user.is_superuser or user.is_staff:
        return

    # إنشاء أو الحصول على مجموعة المستخدمين العاديين
    basic_group, created = Group.objects.get_or_create(
        name='مستخدم عادي',
        defaults={}
    )

    # إضافة صلاحيات أساسية للمجموعة إذا كانت جديدة
    if created or not basic_group.permissions.exists():
        basic_permissions = [
            'view_member',
            'view_payment',
            'view_association',
        ]

        for perm_code in basic_permissions:
            try:
                permission = Permission.objects.get(codename=perm_code)
                basic_group.permissions.add(permission)
            except Permission.DoesNotExist:
                pass

    # إضافة المستخدم للمجموعة
    if not user.groups.filter(name='مستخدم عادي').exists():
        user.groups.add(basic_group)

def logout_view(request):
    logout(request)
    messages.info(request, 'تم تسجيل الخروج بنجاح.')
    return redirect('login')

def check_user_permissions(user):
    """التحقق من صلاحيات المستخدم وإرجاع قائمة بما يمكنه الوصول إليه"""
    # التأكد من أن المستخدم لديه صلاحيات أساسية
    ensure_user_has_basic_permissions(user)

    permissions = {
        'can_view_dashboard': True,  # جميع المستخدمين يمكنهم رؤية لوحة التحكم
        'can_view_members': user.has_perm('core.view_member') or user.is_staff or user.groups.filter(name='مستخدم عادي').exists(),
        'can_add_members': user.has_perm('core.add_member') or user.is_staff,
        'can_edit_members': user.has_perm('core.change_member') or user.is_staff,
        'can_delete_members': user.has_perm('core.delete_member') or user.is_superuser,
        'can_view_payments': user.has_perm('core.view_payment') or user.is_staff or user.groups.filter(name='مستخدم عادي').exists(),
        'can_add_payments': user.has_perm('core.add_payment') or user.is_staff,
        'can_view_reports': user.is_staff or user.has_perm('core.view_member') or user.groups.filter(name='مستخدم عادي').exists(),
        'can_view_settings': user.is_staff,
        'can_export_data': user.is_staff or user.has_perm('core.view_member'),
    }
    return permissions

@login_required
def dashboard(request):
    # التحقق من صلاحيات المستخدم
    user_permissions = check_user_permissions(request.user)

    # Get data from local database based on permissions
    context = {
        'user_permissions': user_permissions,
        'user_name': request.user.get_full_name() or request.user.username,
    }

    # إضافة البيانات حسب الصلاحيات
    if user_permissions['can_view_members']:
        members_count = Member.objects.count()
        context['members_count'] = members_count

        # Recent members for authorized users
        if user_permissions['can_view_members']:
            recent_members = Member.objects.order_by('-created_at')[:5]
            context['recent_members'] = recent_members
    else:
        context['members_count'] = 0
        context['recent_members'] = []

    if user_permissions['can_view_payments']:
        payments_count = Payment.objects.count()
        total_paid = Payment.objects.aggregate(total=Sum('amount'))['total'] or 0

        # Calculate total due
        active_members = Member.objects.filter(status='active').count() if user_permissions['can_view_members'] else 0
        current_month = timezone.now().date().replace(day=1)
        paid_this_month = Payment.objects.filter(
            month=current_month,
            payment_type='monthly'
        ).count()
        total_due = (active_members - paid_this_month) * 50  # Assuming 50 shekels is monthly fee

        # Recent payments
        recent_payments = Payment.objects.select_related('member').order_by('-created_at')[:5]

        context.update({
            'payments_count': payments_count,
            'total_paid': total_paid,
            'total_due': total_due,
            'recent_payments': recent_payments,
        })
    else:
        context.update({
            'payments_count': 0,
            'total_paid': 0,
            'total_due': 0,
            'recent_payments': [],
        })

    return render(request, 'dashboard.html', context)

@login_required
def members(request):
    # التحقق من صلاحيات عرض الأعضاء
    user_permissions = check_user_permissions(request.user)
    if not user_permissions['can_view_members']:
        messages.error(request, 'ليس لديك صلاحية لعرض الأعضاء.')
        return redirect('dashboard')

    # Search functionality
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    members = Member.objects.all()

    if search_query:
        members = members.filter(
            Q(name__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(phone__icontains=search_query)
        )

    if status_filter:
        members = members.filter(status=status_filter)

    # Pagination
    paginator = Paginator(members, 20)  # Show 20 members per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # التحقق من صلاحيات الإضافة والتعديل
    user_permissions = check_user_permissions(request.user)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_choices': Member.STATUS_CHOICES,
        'user_permissions': user_permissions,
    }
    return render(request, 'members.html', context)

@login_required
def add_member(request):
    if request.method == 'POST':
        try:
            # Get or create default association
            association, created = Association.objects.get_or_create(
                name='الجمعية الافتراضية',
                defaults={
                    'description': 'الجمعية الافتراضية',
                    'address': 'العنوان الافتراضي',
                    'phone': '0500000000',
                    'registration_number': 'DEFAULT001',
                    'established_date': timezone.now().date(),
                    'monthly_contribution': 100.00,
                }
            )

            member = Member.objects.create(
                association=association,
                name=request.POST['name'],
                national_id=request.POST['national_id'],
                phone=request.POST['phone'],
                email=request.POST.get('email', ''),
                address=request.POST.get('address', ''),
                status=request.POST['status'],
                joined_at=request.POST['joined_at'],
                notes=request.POST.get('notes', ''),
            )

            # Sync to Firebase if available
            firebase_data = {
                'name': member.name,
                'national_id': member.national_id,
                'phone': member.phone,
                'status': member.status,
                'joined_at': str(member.joined_at),
            }
            firebase.add_member(firebase_data)

            messages.success(request, 'تم إضافة العضو بنجاح')
            return redirect('members')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    return render(request, 'add_member.html')

@login_required
def edit_member(request, member_id):
    member = get_object_or_404(Member, id=member_id)

    if request.method == 'POST':
        try:
            member.name = request.POST['name']
            member.national_id = request.POST['national_id']
            member.phone = request.POST['phone']
            member.email = request.POST.get('email', '')
            member.address = request.POST.get('address', '')
            member.status = request.POST['status']
            member.joined_at = request.POST['joined_at']
            member.notes = request.POST.get('notes', '')
            member.save()

            # Sync to Firebase if available
            firebase_data = {
                'name': member.name,
                'national_id': member.national_id,
                'phone': member.phone,
                'status': member.status,
                'joined_at': str(member.joined_at),
            }
            firebase.update_member(str(member.id), firebase_data)

            messages.success(request, 'تم تحديث بيانات العضو بنجاح')
            return redirect('members')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    return render(request, 'edit_member.html', {'member': member})

@login_required
def delete_member(request, member_id):
    member = get_object_or_404(Member, id=member_id)

    if request.method == 'POST':
        try:
            # Delete from Firebase first
            firebase.delete_member(str(member.id))

            # Delete from local database
            member.delete()

            messages.success(request, 'تم حذف العضو بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    return redirect('members')

@login_required
def payments(request):
    # التحقق من صلاحيات عرض الدفعات
    user_permissions = check_user_permissions(request.user)
    if not user_permissions['can_view_payments']:
        messages.error(request, 'ليس لديك صلاحية لعرض الدفعات.')
        return redirect('dashboard')

    # Search and filter functionality
    search_query = request.GET.get('search', '')
    member_filter = request.GET.get('member', '')
    payment_type_filter = request.GET.get('payment_type', '')

    payments = Payment.objects.select_related('member').all()

    if search_query:
        payments = payments.filter(
            Q(member__name__icontains=search_query) |
            Q(receipt_number__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    if member_filter:
        payments = payments.filter(member_id=member_filter)

    if payment_type_filter:
        payments = payments.filter(payment_type=payment_type_filter)

    # Pagination
    paginator = Paginator(payments, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get members for filter dropdown
    members = Member.objects.filter(status='active').order_by('name')

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'member_filter': member_filter,
        'payment_type_filter': payment_type_filter,
        'members': members,
        'payment_type_choices': Payment.PAYMENT_TYPE_CHOICES,
    }
    return render(request, 'payments.html', context)

@login_required
def add_payment(request):
    if request.method == 'POST':
        try:
            member = get_object_or_404(Member, id=request.POST['member'])

            # Generate receipt number
            last_payment = Payment.objects.order_by('-id').first()
            receipt_number = f"REC{(last_payment.id + 1) if last_payment else 1:06d}"

            payment = Payment.objects.create(
                member=member,
                amount=request.POST['amount'],
                date=request.POST['date'],
                payment_type=request.POST['payment_type'],
                month=request.POST['month'],
                notes=request.POST.get('notes', ''),
                receipt_number=receipt_number,
                created_by=request.user,
            )

            # Sync to Firebase if available
            firebase_data = {
                'member_name': member.name,
                'amount': str(payment.amount),
                'date': str(payment.date),
                'payment_type': payment.payment_type,
                'month': str(payment.month),
                'notes': payment.notes,
                'receipt_number': payment.receipt_number,
            }
            firebase.add_payment(firebase_data)

            messages.success(request, 'تم إضافة الدفعة بنجاح')
            return redirect('payments')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    members = Member.objects.filter(status='active').order_by('name')
    context = {
        'members': members,
        'payment_type_choices': Payment.PAYMENT_TYPE_CHOICES,
    }
    return render(request, 'add_payment.html', context)

@login_required
def reports(request):
    # Monthly report data
    current_month = timezone.now().date().replace(day=1)
    monthly_payments = Payment.objects.filter(
        date__gte=current_month,
        payment_type='monthly'
    ).aggregate(total=Sum('amount'))['total'] or 0

    # Member statistics
    total_members = Member.objects.count()
    active_members = Member.objects.filter(status='active').count()
    inactive_members = Member.objects.filter(status='inactive').count()

    # Payment statistics
    total_payments = Payment.objects.aggregate(total=Sum('amount'))['total'] or 0
    monthly_payments_count = Payment.objects.filter(
        date__gte=current_month
    ).count()

    # Recent activities
    recent_payments = Payment.objects.select_related('member').order_by('-created_at')[:10]

    context = {
        'monthly_payments': monthly_payments,
        'total_members': total_members,
        'active_members': active_members,
        'inactive_members': inactive_members,
        'total_payments': total_payments,
        'monthly_payments_count': monthly_payments_count,
        'recent_payments': recent_payments,
        'current_month': current_month,
    }
    return render(request, 'reports.html', context)

@login_required
def settings_view(request):
    # Get or create default association
    association, created = Association.objects.get_or_create(
        name='الجمعية الافتراضية',
        defaults={
            'description': 'الجمعية الافتراضية',
            'address': 'العنوان الافتراضي',
            'phone': '0500000000',
            'registration_number': 'DEFAULT001',
            'established_date': timezone.now().date(),
            'monthly_contribution': 100.00,
        }
    )

    if request.method == 'POST':
        try:
            association.name = request.POST['name']
            association.description = request.POST.get('description', '')
            association.address = request.POST['address']
            association.phone = request.POST['phone']
            association.email = request.POST.get('email', '')
            association.monthly_contribution = request.POST['monthly_contribution']
            association.save()

            messages.success(request, 'تم حفظ الإعدادات بنجاح')
            return redirect('settings')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'association': association,
        'firebase_enabled': firebase.is_firebase_enabled(),
    }
    return render(request, 'settings.html', context)

@login_required
def export_members_csv(request):
    members = Member.objects.all().order_by('name')

    response = HttpResponse(content_type='text/csv; charset=utf-8')
    response['Content-Disposition'] = 'attachment; filename="members.csv"'

    # Add BOM for proper UTF-8 encoding in Excel
    response.write('\ufeff')

    writer = csv.writer(response)
    writer.writerow([
        'الاسم', 'رقم الهوية', 'رقم الجوال', 'البريد الإلكتروني',
        'العنوان', 'حالة الاشتراك', 'تاريخ الانضمام', 'ملاحظات'
    ])

    for member in members:
        writer.writerow([
            member.name,
            member.national_id,
            member.phone,
            member.email,
            member.address,
            member.get_status_display(),
            member.joined_at,
            member.notes
        ])

    return response

@login_required
def export_payments_csv(request):
    payments = Payment.objects.select_related('member').order_by('-date')

    response = HttpResponse(content_type='text/csv; charset=utf-8')
    response['Content-Disposition'] = 'attachment; filename="payments.csv"'

    # Add BOM for proper UTF-8 encoding in Excel
    response.write('\ufeff')

    writer = csv.writer(response)
    writer.writerow([
        'اسم العضو', 'المبلغ', 'تاريخ الدفع', 'نوع الدفعة',
        'الشهر المدفوع عنه', 'رقم الإيصال', 'ملاحظات'
    ])

    for payment in payments:
        writer.writerow([
            payment.member.name,
            payment.amount,
            payment.date,
            payment.get_payment_type_display(),
            payment.month,
            payment.receipt_number,
            payment.notes
        ])

    return response
