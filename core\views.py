
from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from . import firebase
from django.http import HttpResponse
import csv

# Login view
def login_view(request):
    if request.method == 'POST':
        username = request.POST['username']
        password = request.POST['password']
        user = authenticate(request, username=username, password=password)
        if user:
            login(request, user)
            return redirect('dashboard')
        else:
            return render(request, 'login.html', {'error': 'بيانات الدخول غير صحيحة'})
    return render(request, 'login.html')

def logout_view(request):
    logout(request)
    return redirect('login')

@login_required
def dashboard(request):
    members = firebase.get_all_members()
    payments = firebase.get_payments().stream()
    members_count = len(members)
    payments_list = [p.to_dict() for p in payments]
    payments_count = len(payments_list)
    total_paid = sum([float(p.get('amount', 0)) for p in payments_list])
    total_due = 0  # يمكن حسابها حسب منطق الاشتراكات
    return render(request, 'dashboard.html', {
        'members_count': members_count,
        'payments_count': payments_count,
        'total_paid': total_paid,
        'total_due': total_due,
    })

@login_required
def members(request):
    members = firebase.get_all_members()
    return render(request, 'members.html', {'members': members})

@login_required
def add_member(request):
    if request.method == 'POST':
        data = {
            'name': request.POST['name'],
            'national_id': request.POST['national_id'],
            'phone': request.POST['phone'],
            'status': request.POST['status'],
            'joined_at': request.POST['joined_at'],
        }
        firebase.add_member(data)
        return redirect('members')
    return render(request, 'add_member.html')

@login_required
def edit_member(request, member_id):
    # ...existing code...
    pass

@login_required
def delete_member(request, member_id):
    firebase.delete_member(member_id)
    return redirect('members')

@login_required
def payments(request):
    payments = [p.to_dict() for p in firebase.get_payments().stream()]
    return render(request, 'payments.html', {'payments': payments})

@login_required
def add_payment(request):
    # ...existing code...
    pass

@login_required
def reports(request):
    # ...existing code...
    pass

@login_required
def settings_view(request):
    # ...existing code...
    pass

@login_required
def export_members_csv(request):
    members = firebase.get_all_members()
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=members.csv'
    writer = csv.writer(response)
    writer.writerow(['الاسم', 'رقم الهوية', 'رقم الجوال', 'حالة الاشتراك', 'تاريخ الانضمام'])
    for m in members:
        writer.writerow([m['name'], m['national_id'], m['phone'], m['status'], m['joined_at']])
    return response

@login_required
def export_payments_csv(request):
    payments = [p.to_dict() for p in firebase.get_payments().stream()]
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=payments.csv'
    writer = csv.writer(response)
    writer.writerow(['اسم العضو', 'التاريخ', 'المبلغ', 'ملاحظة'])
    for p in payments:
        writer.writerow([p.get('member_name'), p.get('date'), p.get('amount'), p.get('note', '')])
    return response
