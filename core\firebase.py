import firebase_admin
from firebase_admin import credentials, firestore
import os

FIREBASE_CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'firebase_config.json')

if not firebase_admin._apps:
    cred = credentials.Certificate(FIREBASE_CONFIG_PATH)
    firebase_admin.initialize_app(cred)

db = firestore.client()

# Collection references
def get_associations():
    return db.collection('associations')

def get_members():
    return db.collection('members')

def get_payments():
    return db.collection('payments')

# Example CRUD functions

def add_member(data):
    return get_members().add(data)

def update_member(member_id, data):
    return get_members().document(member_id).set(data, merge=True)

def delete_member(member_id):
    return get_members().document(member_id).delete()

def get_all_members():
    return [doc.to_dict() | {'id': doc.id} for doc in get_members().stream()]

# Similar functions for associations and payments can be added
