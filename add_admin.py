#!/usr/bin/env python
"""
سكريبت إضافة مدير جديد لمنصة تضامن
"""

import os
import sys
import django
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError

def setup_django():
    """إعداد Django"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
    django.setup()

def validate_password(password):
    """التحقق من قوة كلمة المرور"""
    if len(password) < 8:
        return False, "كلمة المرور يجب أن تكون 8 أحرف على الأقل"
    
    if password.isdigit():
        return False, "كلمة المرور لا يمكن أن تكون أرقام فقط"
    
    if password.lower() in ['password', '12345678', 'admin123']:
        return False, "كلمة المرور ضعيفة جداً"
    
    return True, "كلمة المرور قوية"

def create_admin():
    """إنشاء مدير جديد"""
    print("=" * 50)
    print("🔐 إضافة مدير جديد لمنصة تضامن")
    print("=" * 50)
    
    # طلب اسم المستخدم
    while True:
        username = input("اسم المستخدم: ").strip()
        if not username:
            print("❌ اسم المستخدم مطلوب")
            continue
        
        if User.objects.filter(username=username).exists():
            print(f"❌ اسم المستخدم '{username}' موجود بالفعل")
            continue
        
        break
    
    # طلب البريد الإلكتروني
    email = input("البريد الإلكتروني (اختياري): ").strip()
    if email and User.objects.filter(email=email).exists():
        print(f"⚠️  البريد الإلكتروني '{email}' مستخدم بالفعل")
        email = ""
    
    # طلب كلمة المرور
    while True:
        password = input("كلمة المرور: ").strip()
        if not password:
            print("❌ كلمة المرور مطلوبة")
            continue
        
        is_valid, message = validate_password(password)
        if not is_valid:
            print(f"❌ {message}")
            continue
        
        password_confirm = input("تأكيد كلمة المرور: ").strip()
        if password != password_confirm:
            print("❌ كلمات المرور غير متطابقة")
            continue
        
        break
    
    # طلب نوع المستخدم
    print("\nنوع المستخدم:")
    print("1. مدير كامل (Superuser) - صلاحيات كاملة")
    print("2. موظف (Staff) - يمكنه دخول لوحة الإدارة فقط")
    print("3. مستخدم عادي - لا يمكنه دخول لوحة الإدارة")
    
    while True:
        user_type = input("اختر نوع المستخدم (1-3): ").strip()
        if user_type in ['1', '2', '3']:
            break
        print("❌ اختيار غير صحيح")
    
    # إنشاء المستخدم
    try:
        if user_type == '1':
            user = User.objects.create_superuser(
                username=username,
                email=email,
                password=password
            )
            user_type_name = "مدير كامل"
        else:
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password
            )
            
            if user_type == '2':
                user.is_staff = True
                user.save()
                user_type_name = "موظف"
            else:
                user_type_name = "مستخدم عادي"
        
        print("\n" + "=" * 50)
        print("✅ تم إنشاء المستخدم بنجاح!")
        print("=" * 50)
        print(f"اسم المستخدم: {username}")
        print(f"البريد الإلكتروني: {email or 'غير محدد'}")
        print(f"نوع المستخدم: {user_type_name}")
        print(f"تاريخ الإنشاء: {user.date_joined.strftime('%Y-%m-%d %H:%M')}")
        
        if user.is_staff:
            print(f"\n🔗 يمكن للمستخدم دخول لوحة الإدارة:")
            print(f"   http://127.0.0.1:8000/admin")
        
        print("\n💡 نصائح أمنية:")
        print("- احتفظ بكلمة المرور في مكان آمن")
        print("- غيّر كلمة المرور بانتظام")
        print("- لا تشارك بيانات الدخول مع أحد")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False
    
    return True

def list_admins():
    """عرض قائمة المديرين"""
    print("=" * 50)
    print("👥 قائمة المديرين الحاليين")
    print("=" * 50)
    
    superusers = User.objects.filter(is_superuser=True)
    staff_users = User.objects.filter(is_staff=True, is_superuser=False)
    
    if superusers:
        print("🔴 المديرين الكاملين (Superusers):")
        for user in superusers:
            status = "نشط" if user.is_active else "معطل"
            last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else "لم يسجل دخول"
            print(f"  • {user.username} ({user.email or 'بدون إيميل'}) - {status} - آخر دخول: {last_login}")
    
    if staff_users:
        print("\n🟡 الموظفين (Staff):")
        for user in staff_users:
            status = "نشط" if user.is_active else "معطل"
            last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else "لم يسجل دخول"
            print(f"  • {user.username} ({user.email or 'بدون إيميل'}) - {status} - آخر دخول: {last_login}")
    
    if not superusers and not staff_users:
        print("لا يوجد مديرين حالياً")

def change_password():
    """تغيير كلمة مرور مستخدم"""
    print("=" * 50)
    print("🔑 تغيير كلمة المرور")
    print("=" * 50)
    
    username = input("اسم المستخدم: ").strip()
    
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        print(f"❌ المستخدم '{username}' غير موجود")
        return False
    
    print(f"تغيير كلمة مرور المستخدم: {user.username}")
    
    while True:
        new_password = input("كلمة المرور الجديدة: ").strip()
        if not new_password:
            print("❌ كلمة المرور مطلوبة")
            continue
        
        is_valid, message = validate_password(new_password)
        if not is_valid:
            print(f"❌ {message}")
            continue
        
        password_confirm = input("تأكيد كلمة المرور: ").strip()
        if new_password != password_confirm:
            print("❌ كلمات المرور غير متطابقة")
            continue
        
        break
    
    user.set_password(new_password)
    user.save()
    
    print("✅ تم تغيير كلمة المرور بنجاح!")
    return True

def main():
    """الدالة الرئيسية"""
    setup_django()
    
    while True:
        print("\n" + "=" * 50)
        print("🔐 إدارة المديرين - منصة تضامن")
        print("=" * 50)
        print("1. إضافة مدير جديد")
        print("2. عرض قائمة المديرين")
        print("3. تغيير كلمة مرور")
        print("0. خروج")
        print("-" * 50)
        
        choice = input("اختر العملية: ").strip()
        
        if choice == '1':
            create_admin()
        elif choice == '2':
            list_admins()
        elif choice == '3':
            change_password()
        elif choice == '0':
            print("👋 شكراً لاستخدام منصة تضامن!")
            break
        else:
            print("❌ اختيار غير صحيح")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == '__main__':
    main()
