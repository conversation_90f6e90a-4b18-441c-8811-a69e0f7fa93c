from django.urls import path
from . import views

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('members/', views.members, name='members'),
    path('members/add/', views.add_member, name='add_member'),
    path('members/edit/<str:member_id>/', views.edit_member, name='edit_member'),
    path('members/delete/<str:member_id>/', views.delete_member, name='delete_member'),
    path('payments/', views.payments, name='payments'),
    path('payments/add/', views.add_payment, name='add_payment'),
    path('reports/', views.reports, name='reports'),
    path('settings/', views.settings_view, name='settings'),
    path('export/members/', views.export_members_csv, name='export_members_csv'),
    path('export/payments/', views.export_payments_csv, name='export_payments_csv'),
]
