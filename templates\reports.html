{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}التقارير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>التقارير والإحصائيات</h2>
    <div class="btn-group">
        <a href="{% url 'export_members_csv' %}" class="btn btn-outline-success">
            <i class="fas fa-download"></i> تصدير الأعضاء
        </a>
        <a href="{% url 'export_payments_csv' %}" class="btn btn-outline-success">
            <i class="fas fa-download"></i> تصدير الدفعات
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3>{{ total_members }}</h3>
                <p class="mb-0">إجمالي الأعضاء</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x mb-2"></i>
                <h3>{{ active_members }}</h3>
                <p class="mb-0">الأعضاء النشطون</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h3>{{ total_payments|floatformat:0 }}</h3>
                <p class="mb-0">إجمالي المدفوعات (ريال)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                <h3>{{ monthly_payments|floatformat:0 }}</h3>
                <p class="mb-0">دفعات هذا الشهر (ريال)</p>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Reports -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> تقرير الأعضاء
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-success">{{ active_members }}</h4>
                        <small>نشط</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-secondary">{{ inactive_members }}</h4>
                        <small>غير نشط</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-primary">{{ total_members }}</h4>
                        <small>الإجمالي</small>
                    </div>
                </div>
                <hr>
                <div class="d-grid">
                    <a href="{% url 'members' %}" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> عرض جميع الأعضاء
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> تقرير الدفعات
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ monthly_payments_count }}</h4>
                        <small>دفعات هذا الشهر</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ total_payments|floatformat:0 }}</h4>
                        <small>إجمالي المبالغ</small>
                    </div>
                </div>
                <hr>
                <div class="d-grid">
                    <a href="{% url 'payments' %}" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> عرض جميع الدفعات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-history"></i> آخر الأنشطة
        </h5>
    </div>
    <div class="card-body">
        {% if recent_payments %}
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>العضو</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>النوع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in recent_payments %}
                    <tr>
                        <td>{{ payment.member.name }}</td>
                        <td class="text-success">{{ payment.amount|floatformat:2 }} ريال</td>
                        <td>{{ payment.date }}</td>
                        <td>
                            <span class="badge bg-primary">{{ payment.get_payment_type_display }}</span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center text-muted py-3">
            <i class="fas fa-inbox fa-2x mb-2"></i>
            <p>لا توجد أنشطة حديثة</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
