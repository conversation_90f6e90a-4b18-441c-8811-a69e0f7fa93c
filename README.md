# Tadamon Cooperative Management Platform

## Overview
منصة لإدارة الجمعيات التعاونية والصناديق المالية المحلية باستخدام Django وFirebase.

## المتطلبات
- Python 3.8+
- Django
- firebase-admin
- xhtml2pdf أو WeasyPrint

## الإعداد والتشغيل
1. إنشاء بيئة افتراضية وتفعيلها:
   ```powershell
   python -m venv venv
   .\venv\Scripts\Activate.ps1
   ```
2. تثبيت المتطلبات:
   ```powershell
   pip install -r requirements.txt
   ```
3. إعداد Firebase:
   - أنشئ مشروع Firebase وفعّل Firestore.
   - حمّل بيانات الخدمة (Service Account) كملف `firebase_config.json` وضعه في جذر المشروع.
4. تشغيل الخادم:
   ```powershell
   python manage.py runserver
   ```

## الملفات الأساسية
- `firebase_config.json`: إعدادات الاتصال بـ Firebase
- `requirements.txt`: جميع الحزم المطلوبة
- `templates/` و `static/`: ملفات الواجهة

## المزايا
- إدارة الأعضاء والدفعات
- تقارير PDF وCSV
- لوحة تحكم إحصائية
- إعدادات الجمعية

## النشر
جاهز للنشر على PythonAnywhere أو أي بيئة استضافة تدعم Django.
