{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>مرحباً بك في منصة تضامن</h2>
    <div class="text-muted">
        <i class="fas fa-calendar"></i> {{ "now"|date:"Y-m-d H:i" }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row text-center mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-gradient-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">عدد الأعضاء</h5>
                        <h2 class="display-4">{{ members_count }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-3x opacity-75"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'members' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-eye"></i> عرض الأعضاء
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-gradient-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">عدد الدفعات</h5>
                        <h2 class="display-4">{{ payments_count }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-3x opacity-75"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'payments' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-eye"></i> عرض الدفعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-gradient-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">المبالغ المدفوعة</h5>
                        <h2 class="display-6">{{ total_paid|floatformat:0 }}</h2>
                        <small>ريال سعودي</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-3x opacity-75"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'reports' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-gradient-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">المبالغ المتأخرة</h5>
                        <h2 class="display-6">{{ total_due|floatformat:0 }}</h2>
                        <small>ريال سعودي</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-3x opacity-75"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'members' %}?status=active" class="btn btn-light btn-sm">
                        <i class="fas fa-search"></i> المتأخرون
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'add_member' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus"></i> إضافة عضو جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'add_payment' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus"></i> تسجيل دفعة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'export_members_csv' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-download"></i> تصدير الأعضاء
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'settings' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> آخر الأعضاء المضافين
                </h5>
            </div>
            <div class="card-body">
                {% if recent_members %}
                <div class="list-group list-group-flush">
                    {% for member in recent_members %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ member.name }}</strong>
                            <br><small class="text-muted">{{ member.national_id }}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{{ member.status|yesno:'success,secondary' }}">
                                {{ member.get_status_display }}
                            </span>
                            <br><small class="text-muted">{{ member.created_at|date:"Y-m-d" }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'members' %}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الأعضاء
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <p>لا توجد أعضاء بعد</p>
                    <a href="{% url 'add_member' %}" class="btn btn-primary">
                        إضافة أول عضو
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave"></i> آخر الدفعات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                <div class="list-group list-group-flush">
                    {% for payment in recent_payments %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ payment.member.name }}</strong>
                            <br><small class="text-muted">{{ payment.receipt_number }}</small>
                        </div>
                        <div class="text-end">
                            <strong class="text-success">{{ payment.amount|floatformat:2 }} ريال</strong>
                            <br><small class="text-muted">{{ payment.date }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'payments' %}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الدفعات
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <p>لا توجد دفعات بعد</p>
                    <a href="{% url 'add_payment' %}" class="btn btn-primary">
                        تسجيل أول دفعة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);

    // Add some animations
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });
});
</script>
{% endblock %}
