{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}الدفعات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>إدارة الدفعات</h2>
    <a href="{% url 'add_payment' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة دفعة جديدة
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <input type="text" class="form-control" name="search"
                       value="{{ search_query }}" placeholder="البحث بالعضو أو رقم الإيصال">
            </div>
            <div class="col-md-3">
                <select class="form-select" name="member">
                    <option value="">جميع الأعضاء</option>
                    {% for member in members %}
                    <option value="{{ member.id }}" {% if member_filter == member.id|stringformat:"s" %}selected{% endif %}>
                        {{ member.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="payment_type">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in payment_type_choices %}
                    <option value="{{ value }}" {% if payment_type_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
            <div class="col-md-2">
                <a href="{% url 'export_payments_csv' %}" class="btn btn-outline-success w-100">
                    <i class="fas fa-download"></i> تصدير CSV
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Payments Table -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>العضو</th>
                        <th>المبلغ</th>
                        <th>تاريخ الدفع</th>
                        <th>نوع الدفعة</th>
                        <th>الشهر</th>
                        <th>رقم الإيصال</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in page_obj %}
                    <tr>
                        <td>
                            <strong>{{ payment.member.name }}</strong>
                            <br><small class="text-muted">{{ payment.member.national_id }}</small>
                        </td>
                        <td>
                            <strong class="text-success">{{ payment.amount|floatformat:2 }} ريال</strong>
                        </td>
                        <td>{{ payment.date }}</td>
                        <td>
                            {% if payment.payment_type == 'monthly' %}
                                <span class="badge bg-primary">{{ payment.get_payment_type_display }}</span>
                            {% elif payment.payment_type == 'penalty' %}
                                <span class="badge bg-warning">{{ payment.get_payment_type_display }}</span>
                            {% elif payment.payment_type == 'loan_repayment' %}
                                <span class="badge bg-info">{{ payment.get_payment_type_display }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ payment.get_payment_type_display }}</span>
                            {% endif %}
                        </td>
                        <td>{{ payment.month|date:"Y-m" }}</td>
                        <td>
                            <code>{{ payment.receipt_number }}</code>
                        </td>
                        <td>
                            {% if payment.notes %}
                                <small>{{ payment.notes|truncatechars:50 }}</small>
                            {% else %}
                                <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h5>لا توجد دفعات</h5>
            <p class="text-muted">ابدأ بإضافة أول دفعة</p>
            <a href="{% url 'add_payment' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة دفعة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
