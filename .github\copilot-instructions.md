<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

- This project is a Django web app for cooperative management using Firebase Firestore as the backend.
- Use firebase-admin for all database operations (no Django ORM for main data models).
- All CRUD for members, payments, and association info must use Firestore.
- Use Django Auth for admin login only (no self-registration).
- Templates use Bootstrap for a clean, mobile-friendly UI.
- PDF reports generated with xhtml2pdf or WeasyPrint.
- CSV export for members and payments.
- Project must be ready for deployment on PythonAnywhere.
