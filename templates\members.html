{% extends 'base.html' %}
{% block title %}الأعضاء{% endblock %}
{% block content %}
<div class="d-flex justify-content-between mb-3">
    <h4>قائمة الأعضاء</h4>
    <a href="{% url 'add_member' %}" class="btn btn-success">إضافة عضو</a>
</div>
<table class="table table-striped">
    <thead>
        <tr>
            <th>الاسم</th>
            <th>رقم الهوية</th>
            <th>رقم الجوال</th>
            <th>حالة الاشتراك</th>
            <th>تاريخ الانضمام</th>
            <th>إجراءات</th>
        </tr>
    </thead>
    <tbody>
        {% for member in members %}
        <tr>
            <td>{{ member.name }}</td>
            <td>{{ member.national_id }}</td>
            <td>{{ member.phone }}</td>
            <td>{{ member.status }}</td>
            <td>{{ member.joined_at }}</td>
            <td>
                <a href="{% url 'edit_member' member.id %}" class="btn btn-sm btn-primary">تعديل</a>
                <a href="{% url 'delete_member' member.id %}" class="btn btn-sm btn-danger">حذف</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<a href="{% url 'export_members_csv' %}" class="btn btn-outline-secondary">تصدير CSV</a>
{% endblock %}
